import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  SectionList,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { formatDistanceToNow, isToday, isYesterday } from "date-fns";
import { Image } from "expo-image";
import { Id } from "../../../convex/_generated/dataModel";
import NotificationsSkeleton from "../../../components/skeletons/notifications-skeleton";

interface Actor {
  _id: Id<"users">;
  _creationTime: number;
  name?: string;
  email: string;
  role: "user" | "seller" | "admin";
  avatarUrl: string | null;
  isFollowing: boolean;
}

interface Notification {
  _id: Id<"notifications">;
  _creationTime: number;
  type: string;
  userId: Id<"users">;
  actorId: Id<"users">;
  read: boolean;
  actor: Actor;
}

export default function NotificationsScreen() {
  const notificationsData = useQuery(api.users.getNotifications, {
    paginationOpts: { numItems: 10, cursor: null },
  }); 
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);

  const handleFollowToggle = async (
    userId: Id<"users">,
    isFollowing: boolean,
  ) => {
    try {
      if (isFollowing) {
        await unfollowUser({ targetUserId: userId });
      } else {
        await followUser({ targetUserId: userId });
      }
    } catch (error) {
      console.error("Error toggling follow:", error);
    }
  };

  const getNotificationSections = (notifications: Notification[]) => {
    const now = new Date();
    const sections: { title: string; data: Notification[] }[] = [];

    const filteredNotifications = notifications.filter(
      (n): n is Notification => n !== null,
    );

    // Group notifications
    const groupedNotifications = filteredNotifications.reduce(
      (acc, notification) => {
        const notificationDate = new Date(notification._creationTime);
        const timeDiff = now.getTime() - notificationDate.getTime();
        const hoursDiff = timeDiff / (1000 * 60 * 60);

        let sectionTitle: string;
        if (hoursDiff < 1) {
          sectionTitle = "New";
        } else if (isToday(notificationDate)) {
          sectionTitle = "Today";
        } else if (isYesterday(notificationDate)) {
          sectionTitle = "Yesterday";
        } else {
          sectionTitle = "Earlier";
        }

        if (!acc[sectionTitle]) {
          acc[sectionTitle] = [];
        }
        acc[sectionTitle]?.push(notification);
        return acc;
      },
      {} as Record<string, Notification[]>,
    );

    // Convert grouped notifications to sections array
    Object.entries(groupedNotifications).forEach(([title, data]) => {
      sections.push({ title, data });
    });

    return sections;
  };

  const renderNotification = ({ item }: { item: Notification }) => {
    if (item.type !== "follow") return null;

    return (
      <View style={styles.notificationItem}>
        <TouchableOpacity
          style={styles.avatarContainer}
          onPress={() => router.push(`/user/${item.actor._id}`)}
        >
          <Image
            style={styles.avatar}
            source={
              item.actor.avatarUrl
                ? { uri: item.actor.avatarUrl }
                : require("../../assets/icons/default-avatar.svg")
            }
            contentFit="cover"
            transition={200}
          />
        </TouchableOpacity>
        <View style={styles.notificationContent}>
          <View style={styles.textContainer}>
            <Text style={styles.username}>{item.actor.name || "User"}</Text>
            <Text style={styles.message}>started following you</Text>
            <Text style={styles.timestamp}>
              {formatDistanceToNow(new Date(item._creationTime), {
                addSuffix: true,
              })}
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.followButton,
              item.actor.isFollowing && styles.followingButton,
            ]}
            onPress={() =>
              handleFollowToggle(item.actor._id, item.actor.isFollowing)
            }
          >
            <Text
              style={[
                styles.followButtonText,
                item.actor.isFollowing && styles.followingButtonText,
              ]}
            >
              {item.actor.isFollowing ? "Unfollow" : "Follow"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderSectionHeader = ({
    section: { title },
  }: {
    section: { title: string };
  }) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionHeaderText}>{title}</Text>
    </View>
  );

  return (
    <SafeAreaView edges={["top"]} style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>Notifications</Text>
        <View style={styles.settingsButton} />
      </View>

      {notificationsData === undefined ? (
        <NotificationsSkeleton />
      ) : notificationsData && notificationsData.page.length > 0 ? (
        <SectionList
          sections={getNotificationSections(notificationsData.page as any)}
          renderItem={renderNotification}
          renderSectionHeader={renderSectionHeader}
          keyExtractor={(item) => item._id.toString()}
          contentContainerStyle={styles.listContainer}
          stickySectionHeadersEnabled={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>Nothing to see here...</Text>
          <Text style={styles.emptyDescription}>
            View notifications from users here
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 4,
  },
  title: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  settingsButton: {
    width: 32,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  listContainer: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: "row",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  notificationContent: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
  },
  username: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
  },
  message: {
    color: "#8E8E93",
    marginTop: 2,
  },
  timestamp: {
    color: "#636366",
    fontSize: 12,
    marginTop: 4,
  },
  followButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#0A84FF",
    marginLeft: 8,
  },
  followingButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#0A84FF",
  },
  followButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  followingButtonText: {
    color: "#0A84FF",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingTop: 120,
  },
  emptyTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 12,
  },
  emptyDescription: {
    color: "#8E8E93",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#1C1C1E",
  },
  sectionHeaderText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "600",
    textTransform: "uppercase",
  },
});
