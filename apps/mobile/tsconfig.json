{"extends": "../../tsconfig.json", "compilerOptions": {"strict": true, "noEmit": true, "skipLibCheck": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "react-native", "types": ["node", "jest"], "allowJs": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@default-avatar.svg": ["assets/icons/default-avatar.svg"], "@icon.png": ["assets/images/icon.png"], "@assets": ["assets"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "app", "lib", "hooks", "constants", "../../packages/backend", "../../packages/lib", "convex"], "exclude": ["node_modules", "convex/_generated"]}